# SRVXP Project Restructuring Plan

## Overview

This document outlines a comprehensive plan to reorganize the SRVXP_cleanup project into a clean, intuitive Next.js structure that follows industry best practices and will be immediately familiar to senior Next.js developers.

## Current vs. Proposed Structure

### Current Structure Issues
- Mixed naming conventions (zaply, SRVXP_cleanup)
- Scattered configuration files
- Unclear component organization
- Legacy context/store dual system
- Orphaned test files and scripts
- Backend mixed with frontend

### Proposed New Structure
```
medical-appointment-app/
├── README.md
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── .env.example
├── .gitignore
│
├── docs/                          # Documentation
│   ├── api.md
│   ├── deployment.md
│   ├── database-schema.md
│   └── development.md
│
├── public/                        # Static assets
│   ├── images/
│   ├── icons/
│   └── translations/
│
├── src/
│   ├── app/                       # Next.js App Router
│   │   ├── (auth)/               # Auth route group
│   │   │   ├── sign-in/
│   │   │   ├── sign-up/
│   │   │   └── layout.tsx
│   │   ├── (dashboard)/          # Dashboard route group
│   │   │   ├── dashboard/
│   │   │   ├── appointments/
│   │   │   ├── profile/
│   │   │   ├── subscription/
│   │   │   └── layout.tsx
│   │   ├── (public)/             # Public pages route group
│   │   │   ├── pricing/
│   │   │   ├── help/
│   │   │   ├── legal/
│   │   │   └── layout.tsx
│   │   ├── api/                  # API routes
│   │   │   ├── appointments/
│   │   │   ├── auth/
│   │   │   ├── payments/
│   │   │   └── webhooks/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   │
│   ├── components/               # React components
│   │   ├── ui/                   # Base UI components (shadcn/ui)
│   │   ├── forms/                # Form components
│   │   ├── layout/               # Layout components
│   │   ├── features/             # Feature-specific components
│   │   │   ├── auth/
│   │   │   ├── appointments/
│   │   │   ├── dashboard/
│   │   │   ├── landing/
│   │   │   ├── payments/
│   │   │   └── profile/
│   │   └── providers/            # Context providers
│   │
│   ├── lib/                      # Utilities and configurations
│   │   ├── auth/                 # Authentication utilities
│   │   ├── database/             # Database utilities
│   │   ├── payments/             # Payment utilities
│   │   ├── translations/         # Translation system
│   │   ├── validations/          # Zod schemas
│   │   ├── constants.ts
│   │   ├── types.ts
│   │   └── utils.ts
│   │
│   ├── hooks/                    # Custom React hooks
│   │   ├── use-auth.ts
│   │   ├── use-appointments.ts
│   │   ├── use-profile.ts
│   │   └── use-subscription.ts
│   │
│   └── stores/                   # Zustand stores
│       ├── auth-store.ts
│       ├── appointments-store.ts
│       ├── profile-store.ts
│       ├── preferences-store.ts
│       └── index.ts
│
├── database/                     # Database related files
│   ├── migrations/
│   ├── seeds/
│   ├── schema.sql
│   └── README.md
│
├── services/                     # External services
│   ├── appointment-worker/       # Puppeteer automation
│   ├── queue-manager/           # Redis queue management
│   └── supabase/               # Supabase functions
│
├── config/                      # Configuration files
│   ├── eslint.config.js
│   ├── prettier.config.js
│   └── components.json
│
└── archives/                    # Archived/unused files
    ├── legacy-contexts/
    ├── old-components/
    ├── test-scripts/
    └── deprecated/
```

## Detailed Migration Plan

### Phase 1: Preparation and Setup (Low Risk)

#### 1.1 Create New Directory Structure
```bash
# Create main directories
mkdir -p medical-appointment-app/{docs,database,services,config,archives}
mkdir -p medical-appointment-app/src/{components/{ui,forms,layout,features,providers},lib/{auth,database,payments,translations,validations},hooks,stores}
mkdir -p medical-appointment-app/src/components/features/{auth,appointments,dashboard,landing,payments,profile}
mkdir -p medical-appointment-app/src/app/{(auth),(dashboard),(public),api}
mkdir -p medical-appointment-app/services/{appointment-worker,queue-manager,supabase}
```

#### 1.2 Archive Unused Files
Move to `archives/` folder:
- `test-*.js` files (root level)
- `check-*.js` files
- `cookies.txt`
- `subcription-card-data.md`
- `subscription-success-tickets.md`
- `zustand-integration-tickets.md`
- `src/examples/`
- `src/stores/template.ts`
- Legacy documentation files

### Phase 2: Component Reorganization (Medium Risk)

#### 2.1 UI Components Migration
```
Current → New Location
src/components/ui/* → src/components/ui/* (keep as-is)
src/components/zaply/ → src/components/features/landing/
components/animated-background.tsx → src/components/ui/animated-background.tsx
```

#### 2.2 Feature Components Organization
```
Current → New Location
src/components/auth/ → src/components/features/auth/
src/components/layout/ → src/components/layout/
src/app/dashboard/ components → src/components/features/dashboard/
src/app/pricing/ components → src/components/features/payments/
```

#### 2.3 Provider Consolidation
```
Current → New Location
src/lib/AuthContext.tsx → src/components/providers/auth-provider.tsx
src/lib/LanguageContext.tsx → src/components/providers/language-provider.tsx
src/lib/FamilyMembersContext.tsx → src/components/providers/family-provider.tsx
src/lib/profile-update-context.tsx → src/components/providers/profile-provider.tsx
src/components/theme-provider.tsx → src/components/providers/theme-provider.tsx
```

### Phase 3: Library and Utilities Reorganization (Medium Risk)

#### 3.1 Authentication System
```
Current → New Location
src/lib/AuthContext.tsx → src/lib/auth/context.ts
src/lib/auth-utils.ts → src/lib/auth/utils.ts
src/lib/auth-store-integration.tsx → archives/legacy-contexts/
src/lib/supabase/ → src/lib/database/supabase/
```

#### 3.2 Translation System
```
Current → New Location
src/lib/translations.ts → src/lib/translations/keys.ts
src/lib/translation-utils.ts → src/lib/translations/utils.ts
src/components/t.tsx → src/lib/translations/component.tsx
src/components/translation-loader.tsx → src/lib/translations/loader.tsx
public/translations/ → public/translations/ (keep)
```

#### 3.3 Database and API
```
Current → New Location
src/lib/family-members/ → src/lib/database/family-members/
src/lib/appointment-requests/ → src/lib/database/appointments/
src/lib/subscription/ → src/lib/payments/subscription/
src/lib/stripe/ → src/lib/payments/stripe/
```

### Phase 4: Store System Cleanup (High Risk)

#### 4.1 Zustand Store Consolidation
```
Current → New Location
src/stores/useUserStore.ts → src/stores/auth-store.ts
src/stores/useUserProfileStore.ts → src/stores/profile-store.ts
src/stores/useAppointmentHistoryStore.ts → src/stores/appointments-store.ts
src/stores/usePreferencesStore.ts → src/stores/preferences-store.ts
src/stores/useLanguageStore.ts → src/stores/language-store.ts
src/stores/useSubscriptionStore.ts → src/stores/subscription-store.ts
src/stores/useAppStore.ts → src/stores/index.ts (main export)
```

#### 4.2 Remove Integration Layer
Archive all `*-store-integration.tsx` files as they'll be replaced with direct store usage.

### Phase 5: Backend Services Separation (High Risk)

#### 5.1 Service Extraction
```
Current → New Location
backend/worker-manager/ → services/queue-manager/
backend/workers/ → services/appointment-worker/
backend/api/ → archives/old-backend/ (if unused)
supabase/ → services/supabase/
```

#### 5.2 Database Migration
```
Current → New Location
migrations/ → database/migrations/
reset_user_data.sql → database/seeds/reset-data.sql
fix_user_id_references.sql → database/migrations/
```

### Phase 6: Configuration Cleanup (Low Risk)

#### 6.1 Root Level Cleanup
```
Current → New Location
eslint.config.mjs → config/eslint.config.js
postcss.config.mjs → config/postcss.config.js
components.json → config/components.json
bunfig.toml → config/bunfig.toml
netlify.toml → config/netlify.toml
```

#### 6.2 Rename Project Files
```
Current → New Location
package.json (name: "medical-dashboard") → package.json (name: "medical-appointment-app")
README.md → docs/legacy-readme.md
Create new README.md with proper project description
```

## Migration Strategy and Dependency Management

### Critical Dependencies to Maintain

#### 1. Import Path Updates
All import statements need to be updated systematically:

```typescript
// Old imports
import { useAuth } from "@/lib/AuthContext"
import { T } from "@/components/t"
import { Button } from "@/components/ui/button"

// New imports
import { useAuth } from "@/lib/auth/context"
import { T } from "@/lib/translations/component"
import { Button } from "@/components/ui/button"
```

#### 2. TypeScript Path Mapping
Update `tsconfig.json` paths:
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"]
    }
  }
}
```

#### 3. Next.js Configuration Updates
Update `next.config.js` webpack configuration to reflect new paths.

### Migration Execution Strategy

#### Step 1: Create Parallel Structure
1. Create new directory structure alongside existing
2. Copy files to new locations
3. Update imports in copied files
4. Test functionality with new structure

#### Step 2: Gradual Migration
1. Start with leaf components (no dependencies)
2. Move utility functions and constants
3. Migrate stores and contexts
4. Update page components last

#### Step 3: Validation and Testing
1. Run build process after each major migration step
2. Test critical user flows
3. Verify all imports resolve correctly
4. Check that all environment variables still work

#### Step 4: Cleanup
1. Remove old files only after confirming new structure works
2. Update all documentation
3. Update deployment scripts if necessary

### Risk Mitigation

#### High-Risk Areas
1. **Store Integration**: The dual context/store system needs careful migration
2. **Authentication Flow**: Critical for app functionality
3. **Translation System**: Used throughout the application
4. **Backend Services**: Complex dependencies with external systems

#### Mitigation Strategies
1. **Incremental Migration**: Move one feature at a time
2. **Parallel Testing**: Keep old structure until new one is fully validated
3. **Rollback Plan**: Maintain ability to revert changes quickly
4. **Comprehensive Testing**: Test all user flows after each phase

### Post-Migration Benefits

1. **Developer Experience**: Intuitive file organization
2. **Maintainability**: Clear separation of concerns
3. **Scalability**: Easier to add new features
4. **Onboarding**: New developers can navigate easily
5. **Best Practices**: Follows Next.js conventions

## Timeline Estimate

- **Phase 1-2**: 2-3 days (Low risk, structural changes)
- **Phase 3-4**: 3-4 days (Medium risk, logic migration)
- **Phase 5**: 2-3 days (High risk, backend separation)
- **Phase 6**: 1 day (Configuration cleanup)
- **Testing & Validation**: 2-3 days
- **Total**: 10-14 days

## Success Criteria

1. All existing functionality works unchanged
2. Build process completes without errors
3. All tests pass (if any exist)
4. Development server starts correctly
5. Production deployment works
6. No broken imports or missing dependencies
7. Clear, intuitive file organization
8. Updated documentation reflects new structure

This restructuring plan will transform the current project into a professional, maintainable Next.js application that follows industry standards and best practices.

## Detailed Migration Scripts and Commands

### Automated Migration Scripts

#### Script 1: Directory Structure Creation
```bash
#!/bin/bash
# create-new-structure.sh

# Create main project directory
mkdir -p medical-appointment-app

# Create source structure
mkdir -p medical-appointment-app/src/{components/{ui,forms,layout,features,providers},lib/{auth,database,payments,translations,validations},hooks,stores}
mkdir -p medical-appointment-app/src/components/features/{auth,appointments,dashboard,landing,payments,profile}
mkdir -p medical-appointment-app/src/app/{(auth)/{sign-in,sign-up},(dashboard)/{dashboard,appointments,profile,subscription},(public)/{pricing,help,legal},api/{appointments,auth,payments,webhooks}}

# Create external directories
mkdir -p medical-appointment-app/{docs,database/{migrations,seeds},services/{appointment-worker,queue-manager,supabase},config,archives}

echo "✅ New directory structure created"
```

#### Script 2: File Migration with Import Updates
```bash
#!/bin/bash
# migrate-files.sh

# Function to update imports in a file
update_imports() {
    local file=$1

    # Update common import paths
    sed -i 's|@/lib/AuthContext|@/lib/auth/context|g' "$file"
    sed -i 's|@/components/t|@/lib/translations/component|g' "$file"
    sed -i 's|@/lib/translations|@/lib/translations/keys|g' "$file"
    sed -i 's|@/components/zaply|@/components/features/landing|g' "$file"
    sed -i 's|@/stores/useUserStore|@/stores/auth-store|g' "$file"
    sed -i 's|@/stores/useUserProfileStore|@/stores/profile-store|g' "$file"

    echo "Updated imports in $file"
}

# Export function for use in find command
export -f update_imports

# Update all TypeScript and JavaScript files
find medical-appointment-app/src -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | while read file; do
    update_imports "$file"
done
```

### Critical Dependency Mapping

#### Import Path Transformation Table
```typescript
// Complete mapping of old → new import paths

// Authentication
"@/lib/AuthContext" → "@/lib/auth/context"
"@/lib/auth-utils" → "@/lib/auth/utils"
"@/components/auth/protected-route" → "@/components/features/auth/protected-route"

// Translations
"@/components/t" → "@/lib/translations/component"
"@/lib/translations" → "@/lib/translations/keys"
"@/lib/translation-utils" → "@/lib/translations/utils"
"@/components/translation-loader" → "@/lib/translations/loader"

// UI Components
"@/components/zaply/*" → "@/components/features/landing/*"
"@/components/ui/*" → "@/components/ui/*" (unchanged)
"@/components/layout/*" → "@/components/layout/*" (unchanged)

// Stores
"@/stores/useUserStore" → "@/stores/auth-store"
"@/stores/useUserProfileStore" → "@/stores/profile-store"
"@/stores/useAppointmentHistoryStore" → "@/stores/appointments-store"
"@/stores/usePreferencesStore" → "@/stores/preferences-store"
"@/stores/useLanguageStore" → "@/stores/language-store"
"@/stores/useSubscriptionStore" → "@/stores/subscription-store"

// Database & API
"@/lib/supabase/*" → "@/lib/database/supabase/*"
"@/lib/family-members/*" → "@/lib/database/family-members/*"
"@/lib/appointment-requests/*" → "@/lib/database/appointments/*"
"@/lib/stripe/*" → "@/lib/payments/stripe/*"

// Hooks
"@/hooks/use-user-profile" → "@/hooks/use-profile"
"@/hooks/use-user-preferences" → "@/hooks/use-preferences"
"@/hooks/use-subscription" → "@/hooks/use-subscription" (unchanged)
```

### Store Migration Strategy

#### Current Store Dependencies
```typescript
// Current complex interdependencies
useAppStore → combines all stores
useUserStore ← useUserProfileStore (user ID dependency)
usePreferencesStore ← useLanguageStore (language sync)
useLanguageStore ← usePreferencesStore (preference updates)
```

#### New Simplified Store Structure
```typescript
// New clean store structure
// stores/index.ts - Main export file
export { useAuthStore } from './auth-store'
export { useProfileStore } from './profile-store'
export { useAppointmentsStore } from './appointments-store'
export { usePreferencesStore } from './preferences-store'
export { useSubscriptionStore } from './subscription-store'

// Remove circular dependencies
// Each store manages its own domain
// Cross-store communication through events or direct calls
```

### Component Migration Checklist

#### High-Priority Components (Core Dependencies)
- [ ] `src/app/layout.tsx` - Root layout
- [ ] `src/app/ClientBody.tsx` - Provider wrapper
- [ ] `src/components/t.tsx` - Translation component
- [ ] `src/lib/AuthContext.tsx` - Authentication context
- [ ] `src/lib/utils.ts` - Core utilities
- [ ] `middleware.ts` - Route protection

#### Medium-Priority Components (Feature Components)
- [ ] Dashboard layout and components
- [ ] Authentication forms
- [ ] Landing page components
- [ ] Payment/subscription components
- [ ] Profile management components

#### Low-Priority Components (Isolated Features)
- [ ] Help/FAQ components
- [ ] Legal pages
- [ ] Admin components
- [ ] Test components

### Database Migration Considerations

#### Schema Dependencies
```sql
-- Current table relationships that must be maintained
auth.users (Supabase)
  ↓ (1:1)
profiles
  ↓ (1:N)
family_members
  ↓ (1:N)
appointment_requests
  ↓ (1:1)
completed_appointments | cancelled_appointments

-- Subscription flow
auth.users
  ↓ (1:1)
subscriptions (Stripe integration)
```

#### Migration Scripts for Database
```sql
-- No schema changes needed, only file organization
-- Ensure all migration files are moved to database/migrations/
-- Update any hardcoded paths in migration scripts
-- Verify Supabase function paths are updated
```

### Environment Variables and Configuration

#### Required Environment Variables (No Changes)
```bash
# These remain the same after migration
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
```

#### Configuration File Updates
```javascript
// next.config.js - Update webpack configuration
const nextConfig = {
  // Update splitChunks paths
  webpack: (config) => {
    config.optimization.splitChunks.cacheGroups = {
      // Update paths to reflect new structure
      routes: {
        test: /[\\/]src[\\/]app[\\/]/,
        name: 'routes',
        priority: 5,
      },
      components: {
        test: /[\\/]src[\\/]components[\\/]/,
        name: 'components',
        priority: 5,
      },
      features: {
        test: /[\\/]src[\\/]components[\\/]features[\\/]/,
        name: 'features',
        priority: 6,
      },
      lib: {
        test: /[\\/]src[\\/]lib[\\/]/,
        name: 'lib',
        priority: 5,
      },
    }
    return config
  }
}
```

### Testing Strategy During Migration

#### Automated Testing Approach
```bash
# 1. Create test script to verify critical paths
#!/bin/bash
# test-migration.sh

echo "🧪 Testing migration..."

# Test build process
npm run build
if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# Test development server
timeout 30s npm run dev &
DEV_PID=$!
sleep 10

# Test if server is responding
curl -f http://localhost:3000 > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Dev server responding"
else
    echo "❌ Dev server not responding"
fi

kill $DEV_PID
```

#### Manual Testing Checklist
- [ ] Home page loads correctly
- [ ] Authentication flow works (sign in/up)
- [ ] Dashboard loads for authenticated users
- [ ] Language switching works
- [ ] Theme switching works
- [ ] Profile management works
- [ ] Appointment booking flow works
- [ ] Payment/subscription flow works
- [ ] All translations display correctly

### Rollback Strategy

#### Quick Rollback Plan
```bash
#!/bin/bash
# rollback.sh

# If migration fails, quickly restore from backup
echo "🔄 Rolling back migration..."

# Restore original structure from git
git checkout HEAD~1 -- .

# Or restore from backup if available
# cp -r backup-original/* .

echo "✅ Rollback complete"
```

#### Incremental Rollback
- Keep original files until migration is fully validated
- Use feature flags to switch between old/new implementations
- Maintain parallel import paths during transition period

### Post-Migration Validation

#### Automated Validation Script
```bash
#!/bin/bash
# validate-migration.sh

echo "🔍 Validating migration..."

# Check for broken imports
echo "Checking for broken imports..."
npm run build 2>&1 | grep -i "module not found" && echo "❌ Broken imports found" || echo "✅ No broken imports"

# Check for missing files
echo "Checking for missing critical files..."
CRITICAL_FILES=(
    "src/app/layout.tsx"
    "src/lib/auth/context.ts"
    "src/lib/translations/component.tsx"
    "src/stores/index.ts"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "medical-appointment-app/$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file missing"
    fi
done

# Verify environment variables are still working
echo "Testing environment variables..."
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Supabase URL found' : '❌ Supabase URL missing')"

echo "🎉 Validation complete"
```

This comprehensive migration plan ensures a smooth transition while maintaining all existing functionality and relationships.

## Implementation Timeline and Task Breakdown

### Week 1: Foundation and Preparation

#### Day 1-2: Project Setup and Structure Creation
**Tasks:**
- [ ] Create new `medical-appointment-app` directory structure
- [ ] Set up git branch for migration: `git checkout -b restructure/project-organization`
- [ ] Run directory creation script
- [ ] Create backup of current structure
- [ ] Update package.json with new project name and structure

**Deliverables:**
- New directory structure created
- Migration branch established
- Backup created

#### Day 3-4: Archive Cleanup and Documentation
**Tasks:**
- [ ] Move orphaned files to `archives/` folder
- [ ] Archive test scripts and temporary files
- [ ] Create new README.md with updated project description
- [ ] Document all environment variables needed
- [ ] Update .gitignore for new structure

**Files to Archive:**
```
archives/
├── legacy-contexts/
│   ├── auth-store-integration.tsx
│   ├── preferences-store-integration.tsx
│   ├── profile-store-integration.tsx
│   └── language-store-integration.tsx
├── test-scripts/
│   ├── test-appointment-api.js
│   ├── test-auth-flow.js
│   ├── test-domain-consistency.js
│   ├── test-form-persistence.js
│   └── test-google-oauth.html
├── old-components/
│   └── zaply/ (renamed to landing)
└── deprecated/
    ├── cookies.txt
    ├── subcription-card-data.md
    ├── subscription-success-tickets.md
    └── zustand-integration-tickets.md
```

#### Day 5: Configuration Migration
**Tasks:**
- [ ] Move configuration files to `config/` directory
- [ ] Update tsconfig.json with new path mappings
- [ ] Update next.config.js for new structure
- [ ] Update ESLint and Prettier configurations
- [ ] Test build process with new configuration

### Week 2: Core System Migration

#### Day 6-7: Library and Utilities Migration
**Tasks:**
- [ ] Migrate authentication system to `src/lib/auth/`
- [ ] Migrate translation system to `src/lib/translations/`
- [ ] Migrate database utilities to `src/lib/database/`
- [ ] Migrate payment utilities to `src/lib/payments/`
- [ ] Update all import statements in migrated files

**Critical Files Migration:**
```
src/lib/auth/
├── context.ts (from AuthContext.tsx)
├── utils.ts (from auth-utils.ts)
├── types.ts (new)
└── hooks.ts (new)

src/lib/translations/
├── keys.ts (from translations.ts)
├── utils.ts (from translation-utils.ts)
├── component.tsx (from t.tsx)
├── loader.tsx (from translation-loader.tsx)
└── types.ts (new)
```

#### Day 8-9: Store System Overhaul
**Tasks:**
- [ ] Migrate and rename all Zustand stores
- [ ] Remove store integration layer
- [ ] Update store dependencies and circular references
- [ ] Create new unified store index
- [ ] Test store functionality

**Store Migration Map:**
```
stores/
├── auth-store.ts (from useUserStore.ts)
├── profile-store.ts (from useUserProfileStore.ts)
├── appointments-store.ts (from useAppointmentHistoryStore.ts)
├── preferences-store.ts (from usePreferencesStore.ts)
├── subscription-store.ts (from useSubscriptionStore.ts)
└── index.ts (main export, from useAppStore.ts)
```

#### Day 10: Component System Reorganization
**Tasks:**
- [ ] Migrate UI components (keep structure)
- [ ] Reorganize feature components by domain
- [ ] Create new provider structure
- [ ] Update component imports throughout app
- [ ] Test component rendering

### Week 3: Application Layer Migration

#### Day 11-12: App Router Restructuring
**Tasks:**
- [ ] Reorganize pages into route groups
- [ ] Create new layout files for each route group
- [ ] Migrate API routes to new structure
- [ ] Update middleware for new paths
- [ ] Test routing functionality

**Route Group Structure:**
```
src/app/
├── (auth)/
│   ├── sign-in/page.tsx
│   ├── sign-up/page.tsx
│   ├── forgot-password/page.tsx
│   └── layout.tsx
├── (dashboard)/
│   ├── dashboard/page.tsx
│   ├── appointments/page.tsx
│   ├── profile/page.tsx
│   ├── subscription/page.tsx
│   └── layout.tsx
├── (public)/
│   ├── pricing/page.tsx
│   ├── help/page.tsx
│   ├── legal/
│   └── layout.tsx
└── api/
    ├── appointments/route.ts
    ├── auth/route.ts
    ├── payments/route.ts
    └── webhooks/route.ts
```

#### Day 13-14: Backend Services Separation
**Tasks:**
- [ ] Move worker manager to `services/queue-manager/`
- [ ] Move Puppeteer workers to `services/appointment-worker/`
- [ ] Move Supabase functions to `services/supabase/`
- [ ] Update service configurations
- [ ] Test backend service connectivity

#### Day 15: Database and Migration Files
**Tasks:**
- [ ] Move migration files to `database/migrations/`
- [ ] Create database documentation
- [ ] Update any hardcoded paths in migrations
- [ ] Test database connectivity
- [ ] Verify all RLS policies still work

### Week 4: Testing and Validation

#### Day 16-17: Comprehensive Testing
**Tasks:**
- [ ] Run automated test suite
- [ ] Manual testing of all user flows
- [ ] Performance testing
- [ ] Build and deployment testing
- [ ] Cross-browser testing

**Testing Checklist:**
- [ ] Authentication flow (sign up, sign in, sign out)
- [ ] Dashboard functionality
- [ ] Appointment booking flow
- [ ] Profile management
- [ ] Subscription/payment flow
- [ ] Language switching
- [ ] Theme switching
- [ ] Mobile responsiveness
- [ ] API endpoints functionality
- [ ] Database operations

#### Day 18-19: Bug Fixes and Optimization
**Tasks:**
- [ ] Fix any issues found during testing
- [ ] Optimize import statements
- [ ] Remove unused dependencies
- [ ] Update documentation
- [ ] Performance optimization

#### Day 20: Final Validation and Deployment
**Tasks:**
- [ ] Final build test
- [ ] Production deployment test
- [ ] Environment variable validation
- [ ] Documentation review
- [ ] Team review and approval

## Risk Assessment and Mitigation

### High-Risk Areas

#### 1. Store System Migration (Risk Level: 9/10)
**Risks:**
- Breaking store dependencies
- Loss of state persistence
- Performance degradation

**Mitigation:**
- Migrate stores one at a time
- Keep parallel implementations during transition
- Extensive testing of state management

#### 2. Authentication Flow (Risk Level: 8/10)
**Risks:**
- Breaking user sessions
- Security vulnerabilities
- OAuth integration issues

**Mitigation:**
- Test authentication thoroughly
- Maintain session compatibility
- Verify all security policies

#### 3. Translation System (Risk Level: 7/10)
**Risks:**
- Missing translations
- Language switching issues
- Performance impact

**Mitigation:**
- Verify all translation keys
- Test language switching
- Maintain translation file structure

### Medium-Risk Areas

#### 4. Component Dependencies (Risk Level: 6/10)
**Risks:**
- Broken component imports
- UI rendering issues
- Styling problems

**Mitigation:**
- Update imports systematically
- Test component rendering
- Verify styling consistency

#### 5. API Routes (Risk Level: 5/10)
**Risks:**
- Broken API endpoints
- Database connectivity issues
- Middleware problems

**Mitigation:**
- Test all API endpoints
- Verify database connections
- Check middleware functionality

### Success Metrics

#### Technical Metrics
- [ ] Build time: < 2 minutes (current baseline)
- [ ] Bundle size: No increase > 10%
- [ ] Page load time: No degradation
- [ ] Test coverage: Maintain current level
- [ ] Zero broken imports
- [ ] Zero TypeScript errors

#### Functional Metrics
- [ ] All user flows work correctly
- [ ] No data loss during migration
- [ ] All integrations (Stripe, Supabase) work
- [ ] Performance maintained or improved
- [ ] Developer experience improved

#### Quality Metrics
- [ ] Code organization score: 9/10
- [ ] Documentation completeness: 100%
- [ ] Team satisfaction: > 8/10
- [ ] Onboarding time for new developers: < 2 hours

## Post-Migration Maintenance

### Immediate Actions (Week 5)
- [ ] Monitor application performance
- [ ] Gather team feedback
- [ ] Update development documentation
- [ ] Create onboarding guide for new structure
- [ ] Archive old migration branch

### Long-term Actions (Month 2-3)
- [ ] Optimize bundle splitting based on new structure
- [ ] Implement additional performance improvements
- [ ] Create component library documentation
- [ ] Establish coding standards for new structure
- [ ] Plan future architectural improvements

This detailed implementation plan provides a clear roadmap for successfully restructuring the SRVXP project into a professional, maintainable Next.js application.
